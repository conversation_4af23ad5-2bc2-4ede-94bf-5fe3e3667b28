#!/bin/bash

# 构建性能测试脚本
# 用于测试不同配置下的构建性能

echo "🚀 构建性能测试开始..."
echo "========================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试次数
TEST_ROUNDS=3

# 结果文件
RESULTS_FILE="build-performance-results.txt"

# 清空结果文件
> $RESULTS_FILE

echo "测试配置: $TEST_ROUNDS 轮测试" | tee -a $RESULTS_FILE
echo "开始时间: $(date)" | tee -a $RESULTS_FILE
echo "========================" | tee -a $RESULTS_FILE

# 函数：执行单次构建测试
run_build_test() {
    local test_name=$1
    local build_command=$2
    
    echo -e "${BLUE}📊 测试: $test_name${NC}"
    echo "测试: $test_name" >> $RESULTS_FILE
    
    local total_time=0
    local times=()
    
    for i in $(seq 1 $TEST_ROUNDS); do
        echo -e "${YELLOW}  第 $i 轮测试...${NC}"
        
        # 清理缓存
        npm run cache:clear > /dev/null 2>&1
        
        # 记录开始时间
        local start_time=$(date +%s%N)
        
        # 执行构建
        if eval $build_command > /dev/null 2>&1; then
            # 记录结束时间
            local end_time=$(date +%s%N)
            local duration=$(( (end_time - start_time) / 1000000 ))
            times+=($duration)
            total_time=$((total_time + duration))
            echo -e "${GREEN}    ✅ 第 $i 轮: ${duration}ms${NC}"
            echo "    第 $i 轮: ${duration}ms" >> $RESULTS_FILE
        else
            echo -e "${RED}    ❌ 第 $i 轮: 构建失败${NC}"
            echo "    第 $i 轮: 构建失败" >> $RESULTS_FILE
        fi
    done
    
    # 计算平均时间
    local avg_time=$((total_time / TEST_ROUNDS))
    
    # 计算最小和最大时间
    local min_time=${times[0]}
    local max_time=${times[0]}
    for time in "${times[@]}"; do
        if [ $time -lt $min_time ]; then
            min_time=$time
        fi
        if [ $time -gt $max_time ]; then
            max_time=$time
        fi
    done
    
    echo -e "${GREEN}  📈 平均时间: ${avg_time}ms${NC}"
    echo -e "${GREEN}  ⚡ 最快时间: ${min_time}ms${NC}"
    echo -e "${GREEN}  🐌 最慢时间: ${max_time}ms${NC}"
    echo ""
    
    echo "  平均时间: ${avg_time}ms" >> $RESULTS_FILE
    echo "  最快时间: ${min_time}ms" >> $RESULTS_FILE
    echo "  最慢时间: ${max_time}ms" >> $RESULTS_FILE
    echo "" >> $RESULTS_FILE
}

# 测试1: 测试环境构建
run_build_test "测试环境构建" "npm run build:test"

# 测试2: 开发环境构建
run_build_test "开发环境构建" "npm run build:dev"

# 测试3: 生产环境构建
run_build_test "生产环境构建" "npm run build:prod"

echo "========================" | tee -a $RESULTS_FILE
echo "结束时间: $(date)" | tee -a $RESULTS_FILE

echo -e "${GREEN}✅ 构建性能测试完成!${NC}"
echo -e "${BLUE}📄 详细结果已保存到: $RESULTS_FILE${NC}"

# 显示构建产物大小
if [ -d "dist" ]; then
    echo -e "${BLUE}📦 构建产物大小:${NC}"
    du -sh dist/* 2>/dev/null | sort -hr | head -10
fi

echo ""
echo -e "${YELLOW}💡 优化建议:${NC}"
echo "1. 如果构建时间过长，考虑启用更多并行处理"
echo "2. 检查是否有不必要的依赖或插件"
echo "3. 确保缓存配置正确"
echo "4. 考虑使用 webpack-bundle-analyzer 分析包大小"
