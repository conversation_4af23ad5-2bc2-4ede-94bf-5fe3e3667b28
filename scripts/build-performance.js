#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');

const METRICS_FILE = 'build-metrics.json';

/**
 * 格式化文件大小
 */
function formatSize(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 格式化时间
 */
function formatTime(ms) {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

/**
 * 获取Git信息
 */
function getGitInfo() {
  try {
    const commit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    const branch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
    return { commit, branch };
  } catch {
    return { commit: 'unknown', branch: 'unknown' };
  }
}

/**
 * 分析构建输出
 */
function analyzeBuildOutput() {
  const distPath = path.resolve('dist');
  if (!fs.existsSync(distPath)) {
    console.log(chalk.red('❌ dist 目录不存在'));
    return null;
  }

  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;
  let fileCount = { js: 0, css: 0, total: 0 };

  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else {
        const size = stat.size;
        totalSize += size;
        fileCount.total++;
        
        if (file.endsWith('.js')) {
          jsSize += size;
          fileCount.js++;
        } else if (file.endsWith('.css')) {
          cssSize += size;
          fileCount.css++;
        }
      }
    });
  }

  walkDir(distPath);

  return {
    totalSize,
    jsSize,
    cssSize,
    fileCount
  };
}

/**
 * 执行构建并记录性能
 */
function buildAndRecord() {
  console.log(chalk.blue('🚀 开始构建性能测试...'));
  
  const startTime = Date.now();
  
  try {
    // 执行构建
    execSync('npm run build:test', { stdio: 'inherit' });
    
    const buildTime = Date.now() - startTime;
    const output = analyzeBuildOutput();
    
    if (!output) return;
    
    const metrics = {
      timestamp: new Date().toISOString(),
      buildTime,
      buildTimeFormatted: formatTime(buildTime),
      git: getGitInfo(),
      output,
      outputFormatted: {
        totalSize: formatSize(output.totalSize),
        jsSize: formatSize(output.jsSize),
        cssSize: formatSize(output.cssSize)
      }
    };
    
    // 读取现有数据
    let allMetrics = [];
    if (fs.existsSync(METRICS_FILE)) {
      try {
        allMetrics = JSON.parse(fs.readFileSync(METRICS_FILE, 'utf8'));
      } catch {
        allMetrics = [];
      }
    }
    
    // 添加新数据
    allMetrics.push(metrics);
    
    // 只保留最近20次记录
    if (allMetrics.length > 20) {
      allMetrics = allMetrics.slice(-20);
    }
    
    // 保存数据
    fs.writeFileSync(METRICS_FILE, JSON.stringify(allMetrics, null, 2));
    
    // 显示结果
    console.log(chalk.green('\n✅ 构建完成!'));
    console.log(chalk.cyan('📊 构建性能数据:'));
    console.log(`  构建时间: ${chalk.yellow(metrics.buildTimeFormatted)}`);
    console.log(`  总大小: ${chalk.yellow(metrics.outputFormatted.totalSize)}`);
    console.log(`  JS大小: ${chalk.yellow(metrics.outputFormatted.jsSize)}`);
    console.log(`  CSS大小: ${chalk.yellow(metrics.outputFormatted.cssSize)}`);
    console.log(`  文件数量: ${chalk.yellow(output.fileCount.total)} (JS: ${output.fileCount.js}, CSS: ${output.fileCount.css})`);
    
  } catch (error) {
    console.log(chalk.red(`❌ 构建失败: ${error.message}`));
    process.exit(1);
  }
}

/**
 * 比较构建性能
 */
function compareBuilds() {
  if (!fs.existsSync(METRICS_FILE)) {
    console.log(chalk.red('❌ 没有找到构建数据文件'));
    return;
  }
  
  try {
    const allMetrics = JSON.parse(fs.readFileSync(METRICS_FILE, 'utf8'));
    
    if (allMetrics.length < 2) {
      console.log(chalk.yellow('⚠️  需要至少2次构建记录才能进行比较'));
      return;
    }
    
    const latest = allMetrics[allMetrics.length - 1];
    const previous = allMetrics[allMetrics.length - 2];
    
    console.log(chalk.blue('📊 构建性能对比'));
    console.log(chalk.gray('=================='));
    
    // 时间对比
    const timeDiff = latest.buildTime - previous.buildTime;
    const timePercent = ((timeDiff / previous.buildTime) * 100).toFixed(1);
    const timeColor = timeDiff > 0 ? chalk.red : chalk.green;
    console.log(`构建时间: ${latest.buildTimeFormatted} vs ${previous.buildTimeFormatted} ${timeColor(`(${timeDiff > 0 ? '+' : ''}${timePercent}%)`)}`);
    
    // 大小对比
    const sizeDiff = latest.output.totalSize - previous.output.totalSize;
    const sizePercent = ((sizeDiff / previous.output.totalSize) * 100).toFixed(1);
    const sizeColor = sizeDiff > 0 ? chalk.red : chalk.green;
    console.log(`总大小: ${latest.outputFormatted.totalSize} vs ${previous.outputFormatted.totalSize} ${sizeColor(`(${sizeDiff > 0 ? '+' : ''}${sizePercent}%)`)}`);
    
    // 显示最近5次构建趋势
    console.log(chalk.cyan('\n📈 最近构建趋势:'));
    const recent = allMetrics.slice(-5);
    recent.forEach((metric, index) => {
      const date = new Date(metric.timestamp).toLocaleString();
      console.log(`  ${index + 1}. ${date} - ${metric.buildTimeFormatted} - ${metric.outputFormatted.totalSize}`);
    });
    
  } catch (error) {
    console.log(chalk.red(`❌ 读取数据失败: ${error.message}`));
  }
}

// 主程序
const action = process.argv[2] || 'build';

switch (action) {
  case 'build':
    buildAndRecord();
    break;
  case 'compare':
    compareBuilds();
    break;
  default:
    console.log(chalk.red(`❌ 未知操作: ${action}`));
    console.log(chalk.gray('可用操作: build, compare'));
    process.exit(1);
}
