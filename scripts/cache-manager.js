#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const chalk = require('chalk');

// 缓存目录配置
const CACHE_DIRS = [
  'node_modules/.cache',
  'dist',
  '.eslintcache'
];

// 获取命令行参数
const action = process.argv[2] || 'basic';

console.log(chalk.blue('🧹 缓存管理工具'));
console.log(chalk.gray('================'));

/**
 * 删除目录或文件
 */
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      if (fs.statSync(dirPath).isDirectory()) {
        execSync(`rm -rf "${dirPath}"`, { stdio: 'inherit' });
        console.log(chalk.green(`✅ 已删除目录: ${dirPath}`));
      } else {
        fs.unlinkSync(dirPath);
        console.log(chalk.green(`✅ 已删除文件: ${dirPath}`));
      }
    } catch (error) {
      console.log(chalk.red(`❌ 删除失败: ${dirPath} - ${error.message}`));
    }
  } else {
    console.log(chalk.yellow(`⚠️  目录不存在: ${dirPath}`));
  }
}

/**
 * 获取目录大小
 */
function getDirSize(dirPath) {
  if (!fs.existsSync(dirPath)) return '0B';
  try {
    const result = execSync(`du -sh "${dirPath}" 2>/dev/null || echo "0B"`, { encoding: 'utf8' });
    return result.split('\t')[0].trim();
  } catch {
    return '0B';
  }
}

/**
 * 基础缓存清理
 */
function basicClean() {
  console.log(chalk.cyan('\n🔧 执行基础缓存清理...'));
  
  // 显示清理前的大小
  console.log(chalk.gray('\n清理前缓存大小:'));
  CACHE_DIRS.forEach(dir => {
    const size = getDirSize(dir);
    console.log(chalk.gray(`  ${dir}: ${size}`));
  });

  // 清理缓存目录
  CACHE_DIRS.forEach(removeDir);
  
  console.log(chalk.green('\n✅ 基础缓存清理完成!'));
}

/**
 * 深度缓存清理
 */
function deepClean() {
  console.log(chalk.cyan('\n🔧 执行深度缓存清理...'));
  
  basicClean();
  
  // 额外清理项
  const extraDirs = [
    'node_modules/.cache',
    '.vscode/.ropeproject',
    '.history',
    'coverage'
  ];
  
  console.log(chalk.cyan('\n清理额外缓存目录...'));
  extraDirs.forEach(removeDir);
  
  console.log(chalk.green('\n✅ 深度缓存清理完成!'));
}

/**
 * 完全重置
 */
function fullReset() {
  console.log(chalk.cyan('\n🔧 执行完全重置...'));
  console.log(chalk.red('⚠️  这将删除 node_modules 并重新安装依赖'));
  
  deepClean();
  
  // 删除 node_modules
  console.log(chalk.cyan('\n删除 node_modules...'));
  removeDir('node_modules');
  
  // 删除 package-lock.json
  console.log(chalk.cyan('\n删除 package-lock.json...'));
  removeDir('package-lock.json');
  
  // 重新安装依赖
  console.log(chalk.cyan('\n重新安装依赖...'));
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log(chalk.green('\n✅ 依赖重新安装完成!'));
  } catch (error) {
    console.log(chalk.red(`❌ 依赖安装失败: ${error.message}`));
  }
  
  console.log(chalk.green('\n✅ 完全重置完成!'));
}

// 执行对应的清理操作
switch (action) {
  case 'basic':
  case '':
    basicClean();
    break;
  case 'deep':
    deepClean();
    break;
  case 'full':
    fullReset();
    break;
  default:
    console.log(chalk.red(`❌ 未知操作: ${action}`));
    console.log(chalk.gray('可用操作: basic, deep, full'));
    process.exit(1);
}

console.log(chalk.blue('\n🎉 缓存管理完成!'));
console.log(chalk.gray('提示: 如果遇到构建问题，请尝试运行 npm run cache:deep'));
