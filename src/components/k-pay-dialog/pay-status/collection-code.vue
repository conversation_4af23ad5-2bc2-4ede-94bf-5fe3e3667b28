<template>
  <div class="pay-content flex flex-c flex-item-center">
    <p class="pay-money">待支付金额:<span class="money" v-text-format.number="payment_fee"></span></p>
    <div class="pay-qr">
      <div id="qrCode" ref="qrCodeDiv"></div>
    </div>
    <span class="scan">用户扫描二维码付款</span>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2';

export default {
  name: 'CollectionCode',
  props: {
    // 生产微信支付码的地址
    codeUrl: {
      type: String,
      default: '',
    },
    // 微信待支付金额
    payment_fee: {
      type: [String, Number],
      default: '',
    },
    order_id: {
      type: String,
      default: '',
    },
    trade_record_id: {
      type: String,
      default: '',
    },
    isStorePay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      successData: {},
    };
  },
  mounted() {},
  methods: {
    // ************微信付款***********
    //  清除定时器
    clearIntervalEvent() {
      clearInterval(this.timer);
      this.timer = null;
    },
    //生成支二维码
    creatQrCode() {
      setTimeout(() => {
        this.$refs.qrCodeDiv.innerHTML = '';
        let qrCode = new QRCode(this.$refs.qrCodeDiv, {
          text: this.codeUrl,
          width: 160,
          height: 160,
          colorDark: '#333333', //二维码颜色
          colorLight: '#ffffff', //二维码背景色
          correctLevel: QRCode.CorrectLevel.L, //容错率，L/M/H
        });
        qrCode._el.title = '';
      }, 300);
    },
    waitPay() {
      this.clearIntervalEvent();
      setTimeout(() => {
        this.creatQrCode();
        this.timer = setInterval(() => {
          let params = {
            order_id: this.order_id,
            trade_record_id: this.trade_record_id,
          };
          const apiName = this.isStorePay ? 'getRPayState' : 'getPaystate';
          this.$api[apiName](params).then(res => {
            if (res.has_pay === '1') {
              this.clearIntervalEvent();
              // 确认支付获取支付成功信息
              this.orderPayConfirm();
            }
          });
        }, 1000);
      }, 300);
    },

    // 确认支付获取支付成功信息
    orderPayConfirm() {
      let params = {
        order_id: this.order_id,
      };
      const apiName = this.isStorePay ? 'getRPayConfirm' : 'orderPayConfirm';
      this.$api[apiName](params).then(res => {
        this.cancel();
        this.successVisible = true;
        this.successData = res;
        this.$emit('payOk', this.successData);
      });
    },
    // 收款弹窗关闭,清理定时器
    codeVisibleChange(val) {
      if (val) {
        this.waitPay();
      } else {
        this.clearIntervalEvent();
        this.cancel();
      }
    },
    cancel() {
      this.$emit('input', false);
    },
  },
};
</script>

<style lang="less" scoped>
.pay-content {
  margin: 0 auto;
  width: 240px;

  .wait-pay-money {
    font-size: 20px;
    line-height: 34px;
    color: #000;
    margin-bottom: 20px;

    .ori-price {
      font-size: 14px;
      color: #999999;
      line-height: 16px;
      text-decoration: line-through;
      margin-left: 8px;
    }
  }

  .pay-title {
    font-weight: 400;
    line-height: 24px;
    color: #666;
    font-size: 13px;
  }

  .pay-money {
    font-size: 16px;

    .money {
      color: rgb(233, 98, 8);
      font-weight: 600;
      font-size: 20px;
    }
  }

  .pay-qr {
    margin-top: 8px;
    width: 178px;
    height: 178px;
    display: flex;
    justify-content: center;
  }

  .scan {
    padding: 8px 12px;
    margin-top: 16px;
    font-size: 13px;
    background: rgba(54, 162, 31, 0.09);
    border-radius: 16px;
    color: #36a21f;
  }
}
</style>
