<template>
  <div ref="messageList" class="message-list" @scroll="handleScroll">
    <div ref="messageInnerList" class="massage-box">
      <!-- 消息列表 -->
      <div
        :class="{
          'message-item': true,
          'message-item-user': message.role === 'user',
          'editing-mode': message.role === 'user' && editingMessageIds.has(message.id),
        }"
        v-for="(message, index) in visibleMessages"
        :key="'message-item-' + index"
        ref="messageItems"
      >
        <div :class="{ 'message-item-ai': message.role !== 'user' }" style="display: flex; flex-direction: column">
          <!-- 工具使用进度显示 -->
          <template
            v-if="message.type === 'AIMessageChunk' && !message.deep_search_steps?.length && !message.isDeepSearching"
          >
            <!-- :steps="message.steps && message.steps.length ? message.steps : getToolsSteps(message)"
            @showSearchResults="emitShowSearchResults"
            @handleOpenQuoteModal="(tools, id) => $emit('handleOpenQuoteModal', tools, id)" -->
            <tools-progress
              :loading="message.isReponsing"
              :type="message.msg_type"
              :message-id="message.id"
              :duration="message.duration"
              :is-stop="message.isStop"
              :is-error="message.error"
            />
          </template>
          <template
            v-if="
              (message.type === 'AIMessageChunk' && message.deep_search_steps?.length) > 0 || message.isDeepSearching
            "
          >
            <deep-research
              :message="message"
              :is-loading="message.isReponsing"
              :is-deep-searching="message.isDeepSearching"
              @open-quote-modal="data => $emit('handleOpenQuoteModal', data, data.messageId)"
              @result-click="$emit('showSearchResults', $event)"
            />
          </template>
          <!-- 消息项 -->
          <message-item
            :message="message"
            :search-results="message.search_results"
            @showSearchResults="handleShowSearchResults"
            @showCitedTooltip="$emit('showCitedTooltip', $event)"
            @hideCitedTooltip="$emit('hideCitedTooltip')"
            @editMessage="handleEditMessage"
            @editingStateChange="handleEditingStateChange"
            @switchAnswer="handleSwitchAnswer"
            :is-loading="isLoading"
          />
        </div>
        <div
          v-if="
            message.role !== 'user' &&
            !message.isStop &&
            !message.isConnectError &&
            !message.isSystemError &&
            !message.isReponsing
          "
          class="answered-action"
        >
          <template v-if="shouldShowAnswerSwitcher(message)">
            <div
              class="switch-btn"
              :class="{ disabled: getAnswerIndex(message) === 0 }"
              @click="switchToPreviousAnswer(message)"
              v-tooltip="'上一条'"
              style="margin-right: 0"
            >
              <svg-icon style="transform: rotate(180deg)" name="AI-next" :size="14" />
            </div>
            <span class="answer-count">{{ getAnswerIndex(message) + 1 }} / {{ getTotalAnswers(message) }}</span>
            <div
              class="switch-btn"
              :class="{ disabled: getAnswerIndex(message) >= getTotalAnswers(message) - 1 }"
              @click="switchToNextAnswer(message)"
              v-tooltip="'下一条'"
              style="margin-left: 0"
            >
              <svg-icon name="AI-next" :size="14" />
            </div>
          </template>
          <div class="copy" @click="handleCopy(message.content)" v-tooltip="'复制'">
            <svg-icon name="ai-copy" :size="14" />
          </div>
          <div
            class="regenerate switch-btn"
            @click="handleRegenerateAnswer(message, index)"
            v-tooltip="'重新回答'"
            v-if="isLastMessage(index)"
          >
            <svg-icon name="AI-reanswer" :size="14" />
          </div>
          <!-- 答案切换器 -->

          <div
            :class="['tread', +message.upvote === 1 && 'active']"
            @click="handleFeedback(message, 'upvote', index, $event)"
            v-tooltip="{ content: '喜欢', placement: 'top', class: 'tread-tooltip-style' }"
          >
            <svg-icon :name="+message.upvote === 1 ? 'AI-like-selected' : 'AI-liked'" :size="14" />
          </div>
          <div
            :class="['tread', +message.oppose === 1 && 'active']"
            @click="handleFeedback(message, 'oppose', index, $event)"
            v-tooltip="{ content: '不喜欢', placement: 'top', class: 'tread-tooltip-style' }"
          >
            <svg-icon :name="+message.oppose === 1 ? 'ai-dislike' : 'ai-tread'" :size="14" />
          </div>
        </div>
        <div
          v-if="
            (message.isStop || message.isSystemError || message.isConnectError) &&
            messages.length - 1 === index &&
            !message.isCopyedMessage
          "
          class="answered-error"
        >
          <div v-if="message.isStop">你停止生成了本次回答</div>
          <!-- 系统错误（error类型）：只显示重新编辑按钮 -->
          <div v-if="message.isSystemError && !message.isConnectError">
            <div class="error-actions">
              <div @click="$emit('setChatInput', message)">重新编辑问题</div>
            </div>
          </div>
          <!-- 连接错误：显示重试按钮 -->
          <div v-if="message.isConnectError">
            <span v-if="message.isRetrying">正在重试连接...</span>
            <span v-else @click="handleRetry(message)">
              <svg-icon name="AI-refresh"></svg-icon>
              加载失败
              <a class="retry-button"> 点击重试 </a>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="recommend-questions" v-if="showQuestions">
      <div
        class="question-item"
        v-for="(question, index) in recommendQuestions"
        :key="index"
        @click="sendQuestion(question)"
      >
        <span>{{ question }}</span>
        <svg-icon name="AI-arrow-right" color="#9d9999" class="ml-8" :size="14"></svg-icon>
      </div>
    </div>
    <div class="bottom-placeholder" :style="{ height: getPlaceholderHeight + 'px' }"></div>
    <div class="scroll-bottom" ref="scrollBottomRef"></div>

    <!-- 点踩反馈原因选择模态框 -->
    <FeedbackReasonModal :visible.sync="feedbackReasonModalVisible" @confirm="handleFeedbackReasonConfirm" />
  </div>
</template>

<script>
import ToolsProgress from './ToolsProgress.vue';
import DeepResearch from './DeepResearch';

import MessageItem from './MessageItem.vue';
import 'highlight.js/styles/github.css';
import { assistantService } from '@/libs/SSE/SSEService';
import { throttle  } from 'lodash-es';
// 防抖函数
// import { LangfuseWeb } from 'langfuse'; // 暂时注释掉，等待兼容性问题解决
import FeedbackReasonModal from './FeedbackReasonModal.vue';
// 新的 tooltip 基于 tippy.js，无需额外导入
export default {
  name: 'MessageList',
  components: {
    ToolsProgress,
    MessageItem,
    DeepResearch,
    FeedbackReasonModal,
  },
  props: {
    messages: {
      type: Array,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    session_id: {
      type: String,
      default: '',
    },
    recommendQuestions: {
      type: Array,
      default: () => [],
    },
    sendQuestion: {
      type: Function,
    },
  },
  inject: {
    getClinicInfo: {
      default: () => () => ({
        clinic_id: '1652',
        clinic_name: '南宁榕树家景晖中医诊所',
      }),
    },
  },
  data() {
    return {
      messageList: null,
      messagesArray: [],
      lastScrollTop: 0,
      isAutoScrolling: true, // 是否启用自动滚动
      userScrolling: false, // 用户是否正在手动滚动
      scrollCooldown: null, // 滚动冷却计时器
      contentMutationObserver: null, // 内容变化监听器
      placeholderHeight: 0, // 底部占位元素高度
      isNewMessage: false, // 是否是新消息
      isNewSession: false,
      langfuseWeb: null,
      isOnline: navigator.onLine, // 网络状态
      networkListeners: [], // 网络状态监听器
      isAutoRetrying: false, // 是否正在自动重试
      // 点踩反馈相关
      feedbackReasonModalVisible: false,
      currentFeedbackMessage: null,
      currentFeedbackIndex: null,
      // 编辑状态跟踪
      editingMessageIds: new Set(),
    };
  },
  created() {
    this.$bus.$on('scrollToBottom', this.handleScrollToBottomButton);
    this.initLangfuse();
  },
  mounted() {
    this.messageList = this.$refs.messageList;
    this.initScrollState();
    this.setupContentObserver();
    this.initNetworkListeners();
  },
  beforeDestroy() {
    clearTimeout(this.scrollCooldown);
    this.scrollCooldown = null;
    this.$bus.$off('scrollToBottom', this.handleScrollToBottomButton);

    if (this.contentMutationObserver) {
      this.contentMutationObserver.disconnect();
      this.contentMutationObserver = null;
    }

    this.cleanupNetworkListeners();
  },
  computed: {
    clinicInfo() {
      return this.getClinicInfo();
    },
    // 只显示当前答案的消息列表
    visibleMessages() {
      return this.messagesArray.filter(msg => {
        // 用户消息始终显示
        if (msg.role === 'user') return true;

        // AI消息只显示当前答案
        if (msg.type === 'AIMessageChunk') {
          return msg.isCurrentAnswer === true; // 只显示明确标记为当前答案的消息
        }

        return true;
      });
    },

    getLastMessage() {
      return this.messages[this.messages.length - 1];
    },
    showQuestions() {
      return (
        !this.isLoading &&
        this.recommendQuestions.length > 0 &&
        !this.getLastMessage.isStop &&
        !this.getLastMessage.isSystemError &&
        !this.getLastMessage.isConnectError
      );
    },
    getPlaceholderHeight() {
      return this.placeholderHeight - (this.showQuestions ? 140 : 0);
    },
    isLastMessage() {
      return index => this.visibleMessages.length - 1 === index;
    },
  },
  watch: {
    session_id: {
      handler(val, oldval) {
        if (val && val !== oldval) {
          this.isNewSession = true;
        } else {
          this.isNewSession = false;
        }
      },
      deep: true,
    },
    'messages.length'(newLength, oldLength) {
      if (newLength <= 0) {
        this.isAutoScrolling = false;
        this.placeholderHeight = 0;
        return;
      }

      // 新增消息时的处理
      if (newLength > oldLength && !this.isNewSession) {
        this.isNewMessage = true;
        this.isAutoScrolling = true;
        this.userScrolling = false;
        this.$nextTick(() => {
          this.handleNewMessage();
        });
      }
      setTimeout(() => {
        this.isNewSession = false;
      }, 100);
    },
    messages: {
      handler(val, oldVal) {
        this.messagesArray = this.$lodash.cloneDeep(val) || [];

        // 检测是否是切换对话（完全不同的消息数组）
        if (val?.length > 0 && oldVal?.length > 0 && val[0]?.id !== oldVal[0]?.id) {
          // 切换对话时，重置占位高度，避免页面空白
          this.placeholderHeight = 0;
          this.isNewMessage = false;

          // 确保滚动到底部并重新初始化滚动状态
          this.$nextTick(() => {
            this.scrollToBottom(false);
            // 重新初始化滚动状态，确保滚动按钮状态正确
            this.initScrollState();
          });
        }
        // 检测是否是从有消息变为无消息（新建会话）
        else if (val?.length === 0 && oldVal?.length > 0) {
          // 新建会话时，重置所有状态
          this.placeholderHeight = 0;
          this.isNewMessage = false;
          this.isAutoScrolling = true;
          this.userScrolling = false;
          // 立即更新滚动按钮状态为隐藏
          this.$emit('setIsShowScrollButton', false);
        }
      },
      immediate: true,
      deep: true,
    },
    getLastMessage: {
      handler(item) {
        if (!item) return;

        // 消息停止或出错时，清除占位并滚动到底部
        if (item.isStop || item.isSystemError || item.isConnectError) {
          this.placeholderHeight = 0;
          this.$nextTick(() => {
            this.scrollToBottom(false);
          });
          return;
        }

        // 消息内容更新时处理
        // if (this.isNewMessage) {
        // 有占位元素时，优先减少占位高度
        if (this.placeholderHeight > 0) {
          this.adjustPlaceholderHeight();
        }
        // 没有占位元素高度时，才进行自动滚动
        else if (this.isAutoScrolling && !this.userScrolling) {
          this.scheduleAutoScroll();
        }
        // }
      },
      deep: true,
      immediate: true,
    },
    'recommendQuestions.length'() {
      // 只有在显示推荐问题且不是用户主动滚动时才自动滚动到底部
      if (this.showQuestions && this.isAutoScrolling && !this.userScrolling) {
        this.$nextTick(() => {
          this.scrollToBottom(false);
        });
      }
    },
  },
  methods: {
    async handleUserFeedback(value, score = 0, comment = null) {
      if (!value?.trace_id) return;
      const idempotencyKey = `${value.id}-${this.session_id}`;
      try {
        const scoreData = {
          traceId: value.trace_id,
          name: 'user_feedback',
          value: score,
          id: idempotencyKey,
        };

        // 只有当comment不为null时才添加comment字段
        if (comment !== null) {
          scoreData.comment = comment;
        }

        // 暂时注释掉langfuse调用，等待兼容性问题解决
        console.log('User feedback (temporarily disabled):', scoreData);
        // await this.langfuseWeb.score(scoreData);
      } catch (e) {
        console.log('🚀 ~ handleUserFeedback ~ e: ', e);
      }
    },
    initLangfuse() {
      // 暂时注释掉langfuse初始化，等待兼容性问题解决
      // this.langfuseWeb = new LangfuseWeb({
      //   publicKey: 'pk-lf-8dd44b0e-d0cc-4cf4-b824-5d784b131bba',
      //   baseUrl: 'https://lf-ykadd54dl0qxnljypyze0.rsjxx.com',
      // });
      console.log('Langfuse temporarily disabled due to compatibility issues');
    },
    // 初始化滚动状态
    initScrollState() {
      this.$nextTick(() => {
        const messageList = this.$refs.messageList;
        if (messageList) {
          const { scrollTop, clientHeight, scrollHeight } = messageList;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
          this.$emit('setIsShowScrollButton', distanceFromBottom > 100);
          this.isAutoScrolling = true;
          // 加载历史消息时不使用平滑滚动
          this.scrollToBottom(false);
        }
      });
    },

    // 处理新消息
    handleNewMessage() {
      const messageList = this.$refs.messageList;
      if (!messageList) return;

      // 先立即滚动到底部（不使用平滑滚动）
      this.scrollToBottom(false);

      // 然后计算占位高度并平滑滚动到顶部
      this.$nextTick(() => {
        this.calculatePlaceholderHeight();
        this.isNewMessage = false;
      });
    },

    // 计算占位元素高度（用于新消息置顶显示）
    calculatePlaceholderHeight() {
      const messageList = this.$refs.messageList;
      const messageItems = this.$refs.messageItems || [];

      if (!messageList || messageItems.length === 0) return;

      // 找到最后一个用户消息 - 使用visibleMessages来匹配渲染的索引
      const lastUserMessageIndex = this.visibleMessages.findLastIndex(msg => msg.role === 'user');
      const userMessageElement = messageItems[lastUserMessageIndex];

      if (userMessageElement) {
        const viewportHeight = messageList.clientHeight;
        const userMessageHeight = userMessageElement.offsetHeight;
        const recommendQsHeight = this.showQuestions ? 140 : 0;
        // 考虑header高度和额外间距，确保用户消息在header下方显示
        const headerHeight = 56; // 顶部header高度
        const extraPadding = 142; // 额外间距

        // 计算需要的占位高度，确保用户消息在header下方显示
        const availableHeight = viewportHeight - headerHeight - extraPadding;
        const remainingHeight = availableHeight - userMessageHeight - recommendQsHeight;
        this.placeholderHeight = Math.max(0, remainingHeight);

        // 平滑滚动到新位置
        this.$nextTick(() => {
          this.scrollToBottom(true);
        });
      }
    },

    // 设置内容变化观察器
    setupContentObserver() {
      if (!window.MutationObserver) return;

      const messageInnerList = this.$refs.messageInnerList;
      if (!messageInnerList) return;

      this.contentMutationObserver = new MutationObserver(() => {
        if (!this.isNewMessage) {
          // 优先处理占位元素高度调整
          if (this.placeholderHeight > 0) {
            // 有占位元素时，调整占位高度
            this.adjustPlaceholderHeight();
            // 如果调整后占位高度变为0，则进行滚动
            if (this.placeholderHeight === 0 && this.isAutoScrolling && !this.userScrolling) {
              this.scheduleAutoScroll();
            }
          }
          // 只有在没有占位元素高度时才自动滚动
          else if (this.isAutoScrolling && !this.userScrolling) {
            this.scheduleAutoScroll();
          }
        }
      });

      this.contentMutationObserver.observe(messageInnerList, {
        childList: true,
        subtree: true,
        characterData: true,
      });
    },

    // 动态调整占位元素高度
    adjustPlaceholderHeight() {
      const messageList = this.$refs.messageList;
      const messageItems = this.$refs.messageItems || [];

      if (!messageList || messageItems.length === 0 || this.placeholderHeight <= 0) return;

      const viewportHeight = messageList.clientHeight;
      const headerHeight = 55;
      const extraPadding = 40;
      const availableHeight = viewportHeight - headerHeight - extraPadding;

      // 找到最后一个AI消息元素 - 使用visibleMessages来匹配渲染的索引
      const lastAIMessageIndex = this.visibleMessages.findLastIndex(msg => msg.role !== 'user');
      const aiMessageElement = messageItems[lastAIMessageIndex];

      if (aiMessageElement) {
        // 获取当前AI消息的高度
        const aiMessageHeight = aiMessageElement.offsetHeight;

        // 计算推荐问题的高度
        const recommendQsHeight = this.showQuestions ? 140 : 0;

        // 计算当前占位高度下，AI消息可以使用的最大高度
        const maxAIMessageHeight = availableHeight - this.placeholderHeight - recommendQsHeight;

        // 只有当AI消息高度超过了当前分配给它的空间时，才减少占位高度
        if (aiMessageHeight > maxAIMessageHeight) {
          // 计算需要为AI消息额外分配的空间
          const extraSpaceNeeded = aiMessageHeight - maxAIMessageHeight;
          // 从占位高度中减去这部分空间，但保留一些缓冲
          const newPlaceholderHeight = Math.max(0, this.placeholderHeight - extraSpaceNeeded - 20);

          // 只有当新高度明显小于当前高度时才调整
          if (newPlaceholderHeight < this.placeholderHeight) {
            this.placeholderHeight = newPlaceholderHeight;
          }
        }
      }
    },

    // 计划自动滚动（节流版本）
    scheduleAutoScroll: throttle(function () {
      requestAnimationFrame(() => {
        if (this.isAutoScrolling && !this.userScrolling) {
          this.scrollToBottom(true);
        }
      });
    }, 300),

    // 处理滚动到底部按钮点击（不使用平滑滚动）
    handleScrollToBottomButton() {
      this.placeholderHeight = 0; // 清除占位高度
      this.isAutoScrolling = true;
      this.userScrolling = false;
      this.scrollToBottom(false); // 不使用平滑滚动
    },

    // 滚动到底部
    scrollToBottom(smooth = true) {
      console.log('scrollToBottom executed');
      const messageList = this.$refs.messageList;
      if (!messageList) return;

      const { scrollHeight } = messageList;
      messageList.scrollTo({
        top: scrollHeight,
        behavior: smooth ? 'smooth' : 'auto',
      });
    },

    // 处理用户滚动事件
    handleScroll: throttle(function () {
      const messageList = this.$refs.messageList;
      if (!messageList) return;

      const { scrollTop, clientHeight, scrollHeight } = messageList;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

      // 计算滚动方向
      const scrollDelta = scrollTop - this.lastScrollTop;
      this.lastScrollTop = scrollTop;

      // 更新滚动按钮显示状态
      this.$emit('setIsShowScrollButton', distanceFromBottom > 100);

      // 用户向上滚动时，禁用自动滚动
      if (scrollDelta < 0) {
        this.userScrolling = true;
        this.isAutoScrolling = false;

        // 清除之前的冷却计时器
        if (this.scrollCooldown) {
          clearTimeout(this.scrollCooldown);
        }

        // 用户停止滚动后的冷却时间
        this.scrollCooldown = setTimeout(() => {
          this.userScrolling = false;
          // 如果用户滚动到底部附近，重新启用自动滚动
          if (distanceFromBottom < 5) {
            this.isAutoScrolling = true;
          }
        }, 1500);
        return;
      }
      // 用户滚动到底部时，重新启用自动滚动
      if (distanceFromBottom <= 20) {
        this.userScrolling = false;
        this.isAutoScrolling = true;

        // 微调到完全底部
        if (distanceFromBottom > 5) {
          this.scheduleAutoScroll();
        }
      }
    }, 300),

    handleShowSearchResults(results) {
      this.$emit('showSearchResults', results);
    },
    emitShowSearchResults(results) {
      this.$emit('showSearchResults', results);
    },
    getToolsSteps(message) {
      const toolsProgress = message.toolsProgress;
      const messageType = toolsProgress.type;
      const steps = [
        { id: 'step1', title: '问题分析', status: 'processing', content: message.step_plann_reasoning },
        { id: 'step2', title: '数据检索', status: 'waiting', tools: toolsProgress.tools },
        { id: 'step3', title: '答案生成', status: 'waiting' },
      ];

      if (messageType === 'step_plann_reasoning_end') {
        steps[0].status = 'done';
      } else if (messageType === 'tools_using') {
        steps[0].status = 'done';
        steps[1].status = 'processing';
      } else if (messageType === 'tools_using_end') {
        steps[0].status = 'done';
        steps[1].status = 'processing';
        steps[2].status = 'waiting';
      } else if (messageType === 'content') {
        steps[0].status = 'done';
        steps[1].status = 'done';
        steps[2].status = 'processing';
      } else if (messageType === 'content_end') {
        steps[0].status = 'done';
        steps[1].status = 'done';
        steps[2].status = 'done';
      } else if (['reasoning', 'reasoning_start', 'reasoning_end'].includes(messageType)) {
        steps[0].status = 'done';
        steps[1].status = 'done';
        steps[2].status = 'waiting';
      }

      return steps.filter(step => {
        if (step.title === '数据检索' && (!step.tools || step.tools.length === 0)) {
          return false;
        }
        return step.status !== 'waiting';
      });
    },

    async handleCopy(content) {
      // 替换所有引用字符：[R@******] 和 [^1] 等格式
      const cleanContent = content.replace(/\[(?:R@|\^)[^\]]*\]/g, '');
      await navigator.clipboard.writeText(cleanContent);
      this.$Message.success('复制成功');
    },

    handleFeedback(message, type, index, event) {
      const { oppose, upvote, id } = message;
      let finalUpvote = type === 'upvote' ? upvote ^ 1 : upvote;
      let finalOppose = type === 'oppose' ? oppose ^ 1 : oppose;

      if (type === 'upvote' && finalUpvote) {
        finalOppose = 0;
      } else if (type === 'oppose' && finalOppose) {
        finalUpvote = 0;
      }

      // 如果是点踩操作（而非取消点踩），先调用内部接口，成功后打开反馈弹窗
      if (type === 'oppose' && finalOppose === 1) {
        // 隐藏点踩按钮的tooltip
        this.hideTooltip(event);

        // 先调用内部点踩接口
        assistantService
          .markChat({
            message_id: id,
            session_id: this.session_id,
            upvote: finalUpvote,
            oppose: finalOppose,
            clinic_name: this.clinicInfo.clinic_name,
            clinic_id: this.clinicInfo.clinic_id,
          })
          .then(() => {
            // 更新UI状态
            this.$set(this.messagesArray[index], 'upvote', finalUpvote);
            this.$set(this.messagesArray[index], 'oppose', finalOppose);
            this.handleUserFeedback(message, 2);
            // 保存当前反馈的消息信息
            this.currentFeedbackMessage = message;
            this.currentFeedbackIndex = index;

            // 打开点踩原因选择弹窗
            this.feedbackReasonModalVisible = true;
          })
          .catch(e => {
            this.$Message.error('反馈失败，请稍后重试');
          });
      } else {
        // 其他情况（点赞或取消点踩）直接调用接口
        assistantService
          .markChat({
            message_id: id,
            session_id: this.session_id,
            upvote: finalUpvote,
            oppose: finalOppose,
            clinic_name: this.clinicInfo.clinic_name,
            clinic_id: this.clinicInfo.clinic_id,
          })
          .then(() => {
            this.$Message.success('感谢您的反馈！');
            this.$set(this.messagesArray[index], 'upvote', finalUpvote);
            this.$set(this.messagesArray[index], 'oppose', finalOppose);

            // 根据新的逻辑调用Langfuse
            let langfuseScore = 0; // 默认取消状态
            if (type === 'upvote' && finalUpvote) {
              langfuseScore = 1; // 点赞
            } else if (type === 'upvote' && finalUpvote === 0) {
              langfuseScore = 0; // 取消点赞
            } else if (type === 'oppose' && finalOppose === 0) {
              langfuseScore = 0; // 取消点踩
            }

            // 点赞或取消操作不传递comment，会清除之前的comment
            this.handleUserFeedback(message, langfuseScore);
          })
          .catch(e => {
            this.$Message.error('反馈失败，请稍后重试');
          });
      }
    },

    // 隐藏tooltip
    hideTooltip(event) {
      if (event && event.currentTarget) {
        const element = event.currentTarget;
        const tippyInstance = element['@@tippyInstance'];
        if (tippyInstance) {
          tippyInstance.hide();
        }
      }
    },

    // 处理点踩反馈原因确认
    async handleFeedbackReasonConfirm(reasonData) {
      if (!this.currentFeedbackMessage) return;

      try {
        // 构建反馈内容
        let comment = '';
        if (reasonData.selectedReasons && reasonData.selectedReasons.length > 0) {
          comment += '选择的原因：' + reasonData.selectedReasons.join('、');
        }
        if (reasonData.customReason && reasonData.customReason.trim()) {
          if (comment) comment += '；';
          comment += '详细说明：' + reasonData.customReason.trim();
        }

        // 调用Langfuse接口 - 点踩为2分，并传递comment
        await this.handleUserFeedback(
          this.currentFeedbackMessage,
          2, // 点踩为2分
          comment
        );

        this.$Message.success('感谢您的反馈！');
      } catch (e) {
        this.$Message.error('提交反馈失败，请稍后重试');
      } finally {
        // 清理状态
        this.currentFeedbackMessage = null;
        this.currentFeedbackIndex = null;
        this.feedbackReasonModalVisible = false;
      }
    },

    // 处理重试按钮点击
    handleRetry(message) {
      // 防止重复重试
      if (message.isRetrying) {
        return;
      }

      this.$emit('retryMessage', message.id);
    },

    // 初始化网络状态监听
    initNetworkListeners() {
      const handleOnline = () => {
        const wasOffline = !this.isOnline;
        this.isOnline = true;

        // 如果之前是断网状态，现在重新联网，自动重连失败的消息
        if (wasOffline) {
          this.autoRetryOnReconnect();
        }
      };

      const handleOffline = () => {
        this.isOnline = false;

        // 断网时处理当前正在响应的消息
        this.handleNetworkDisconnect();
      };

      // 添加事件监听
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // 保存监听器引用，用于清理
      this.networkListeners = [
        { event: 'online', handler: handleOnline },
        { event: 'offline', handler: handleOffline },
      ];
    },

    // 处理断网时的活跃连接
    handleNetworkDisconnect() {
      console.log(
        '处理断网，当前消息列表:',
        this.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          isReponsing: msg.isReponsing,
          isSystemError: msg.isSystemError,
          isConnectError: msg.isConnectError,
        }))
      );

      // 找到当前正在响应的消息（没有错误状态的）
      const respondingMessage = this.messages.find(
        msg => msg.role === 'assistant' && msg.isReponsing && !msg.isSystemError && !msg.isConnectError
      );

      if (respondingMessage) {
        // 将消息置为连接异常状态（不可重新编辑，只能重试）
        this.$set(respondingMessage, 'isSystemError', false);
        this.$set(respondingMessage, 'isConnectError', true);
        this.$set(respondingMessage, 'canRetry', true);
        this.$set(respondingMessage, 'isReponsing', false);

        // 通知父组件停止loading
        this.$emit('networkDisconnected', respondingMessage.id);
      } else {
      }
    },

    // 重新联网时自动重试
    autoRetryOnReconnect() {
      // 防止重复自动重试
      if (this.isAutoRetrying) {
        return;
      }

      // 找到最后一条错误的消息（使用新的错误状态字段）
      const lastErrorMessage = this.messages
        .slice()
        .reverse()
        .find(
          msg =>
            msg.role === 'assistant' && (msg.isSystemError || msg.isConnectError) && msg.canRetry && !msg.isRetrying // 确保消息没有在重试中
        );

      if (lastErrorMessage) {
        this.isAutoRetrying = true;

        // 延迟一下再重试，确保网络完全恢复
        setTimeout(() => {
          // 再次检查消息状态，防止在延迟期间用户手动重试了
          if (
            !lastErrorMessage.isRetrying &&
            lastErrorMessage.canRetry &&
            (lastErrorMessage.isSystemError || lastErrorMessage.isConnectError)
          ) {
            this.handleRetry(lastErrorMessage);
          } else {
          }

          // 重置自动重试状态
          this.isAutoRetrying = false;
        }, 1000);
      } else {
      }
    },

    // 清理网络状态监听
    cleanupNetworkListeners() {
      if (this.networkListeners) {
        this.networkListeners.forEach(({ event, handler }) => {
          window.removeEventListener(event, handler);
        });
        this.networkListeners = [];
      }
    },

    // 处理编辑消息
    handleEditMessage({ messageId, newContent, groupId, is_edit }) {
      console.log('编辑消息 - messageId:', messageId, 'groupId: ', groupId, 'is_edit: ', is_edit);

      // 找到要编辑的消息
      const messageIndex = this.messagesArray.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        this.$Message.error('未找到要编辑的消息');
        return;
      }

      const originalMessage = this.messagesArray[messageIndex];
      const targetGroupId = groupId || originalMessage.group_id;

      console.log('原消息:', originalMessage);
      console.log('目标group_id:', targetGroupId);
      console.log('删除前消息数量:', this.messagesArray.length);
      console.log(
        '所有消息的group_id:',
        this.messagesArray.map(msg => ({ id: msg.id, group_id: msg.group_id, role: msg.role }))
      );

      // 发射事件给父组件，先删除相关消息，然后重新发送消息
      this.$emit('editAndResendMessage', {
        groupId: targetGroupId,
        content: newContent,
        is_edit: true,
        deleteGroupId: targetGroupId, // 添加要删除的group_id
      });
    },

    // 处理编辑状态变化
    handleEditingStateChange({ messageId, isEditing }) {
      if (isEditing) {
        this.editingMessageIds.add(messageId);
      } else {
        this.editingMessageIds.delete(messageId);
      }
    },

    // 处理重新回答
    handleRegenerateAnswer(message, index) {
      // 找到对应的用户问题
      let userMessage = null;
      for (let i = index - 1; i >= 0; i--) {
        if (this.visibleMessages[i].role === 'user') {
          userMessage = this.visibleMessages[i];
          break;
        }
      }

      if (!userMessage) {
        this.$Message.error('未找到对应的用户问题');
        return;
      }

      // 发射重新回答事件
      this.$emit('regenerateAnswer', {
        questionId: message.group_id, // 使用用户问题的ID
        questionContent: userMessage.content,
        currentAnswerId: message.group_id,
        originalMessageIndex: index, // 传递原消息索引用于后续处理
      });
    },

    // 处理答案切换
    handleSwitchAnswer({ groupId, targetIndex }) {
      // 找到该group_id的所有答案
      const answers = this.messagesArray
        .filter(msg => msg.type === 'AIMessageChunk' && msg.group_id === groupId)
        .sort((a, b) => a.answerIndex - b.answerIndex);

      if (targetIndex >= 0 && targetIndex < answers.length) {
        // 隐藏所有答案
        answers.forEach(answer => {
          this.$set(answer, 'isCurrentAnswer', false);
        });

        // 显示目标答案
        this.$set(answers[targetIndex], 'isCurrentAnswer', true);
      }
    },

    // 是否显示答案切换器
    shouldShowAnswerSwitcher(message) {
      return message.type === 'AIMessageChunk' && this.getTotalAnswers(message) > 1 && message.isCurrentAnswer;
    },

    // 获取该group_id的总答案数
    getTotalAnswers(message) {
      if (message.type !== 'AIMessageChunk' || !message.group_id) return 1;
      return this.messagesArray.filter(msg => msg.type === 'AIMessageChunk' && msg.group_id === message.group_id)
        .length;
    },

    // 获取当前答案的索引
    getAnswerIndex(message) {
      return message.answerIndex || 0;
    },

    // 切换到上一个答案
    switchToPreviousAnswer(message) {
      if (this.getAnswerIndex(message) > 0) {
        this.handleSwitchAnswer({
          groupId: message.group_id,
          targetIndex: this.getAnswerIndex(message) - 1,
        });
      }
    },

    // 切换到下一个答案
    switchToNextAnswer(message) {
      if (this.getAnswerIndex(message) < this.getTotalAnswers(message) - 1) {
        this.handleSwitchAnswer({
          groupId: message.group_id,
          targetIndex: this.getAnswerIndex(message) + 1,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
/* Style section remains unchanged as it is identical in Vue 2 and Vue 3 */
.message-list {
  overflow-y: auto;
  margin-top: 16px;
  margin: auto;
  width: 100%;
  height: 100%;
  padding-bottom: 42px;
  padding-top: 56px;
  &::-webkit-scrollbar {
    display: none;
    opacity: 0;
  }
  .massage-box {
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    padding-top: 16px;
    .message-item {
      width: 100%;
      // margin-top: 16px;
      .answered-action {
        width: 100%;
        height: 16px;
        // margin-top: 6px;
        display: flex;
        align-items: center;
        padding-left: 12px;
        .copy,
        .regenerate,
        .tread,
        .switch-btn {
          color: #888888;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          stroke: #888888;
          &.active {
            color: #115bd4;
            &:hover {
              color: #3a7bdd !important;
            }
          }
        }
        .tread,
        .switch-btn {
          margin-left: 8px;
        }
        .copy:hover,
        .tread:hover,
        .switch-btn:hover:not(.disabled) {
          color: #333333 !important;
          background: #f5f6f8;
        }
        .switch-btn.disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }
        .answer-count {
          font-size: 12px;
          color: #666666;
          font-weight: 500;
          margin-left: 2px;
          margin-right: 2px;
        }
      }
      .answered-error {
        margin-top: 16px;
        padding-bottom: 50px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        > div:first-child {
          font-size: 12px;
          color: #999999;
          line-height: 18px;
          margin-bottom: 8px;
        }

        .error-actions {
          display: flex;
          align-items: center;
          gap: 12px;

          > div {
            font-size: 12px;
            color: #155bd4;
            line-height: 18px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              color: #3a7bdd;
              background-color: #f0f7ff;
            }
          }

          .retry-button {
            background-color: #155bd4;
            color: #ffffff;

            &:hover {
              background-color: #3a7bdd;
              color: #ffffff;
            }
          }
        }
      }
    }
    .message-item-user {
      margin-top: 16px;
      padding-right: 16px;
      // max-width: 85%;
      align-self: flex-end;
    }

    // 编辑状态下的用户消息占满全宽
    .message-item-user.editing-mode {
      max-width: 100% !important;
      padding-right: 0;
    }
    .message-item-ai {
      padding: 16px;
      background: #ffffff;
      border-radius: 0 10px 10px 10px;
    }
    .message-item-user:first-child {
      margin-top: 0;
    }

    // 底部占位元素样式
    .bottom-placeholder {
      width: 100%;
      transition: height 0.6s ease;
    }
  }
}

.recommend-questions {
  width: 100%;
  margin-top: 22px;
  display: flex;
  flex-wrap: wrap;
  .question-item {
    font-size: 12px;
    color: #333333;
    margin-right: 12px;
    line-height: 18px;
    padding: 7px 12px;
    border: 1px solid rgba(220, 221, 224, 0.8);
    border-radius: 8px;
    margin-bottom: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    &:hover {
      color: #666666;
      background-color: #f5f6f8;
    }

    &:last-of-type {
      margin-right: 0;
    }
  }
}
</style>
