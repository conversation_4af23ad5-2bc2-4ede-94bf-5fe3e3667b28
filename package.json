{"name": "fpc-clinic", "version": "0.1.0", "private": true, "scripts": {"serve:dev": "vue-cli-service serve --mode serve.dev", "serve:test": "vue-cli-service serve --mode serve.test", "serve:prod": "vue-cli-service serve --mode serve.prod", "serve:test-74": "vue-cli-service serve --mode serve.test-74", "build:test-74": "vue-cli-service build --mode build.test-74", "build:dev": "vue-cli-service build --mode build.dev", "build:test": "vue-cli-service build --mode build.test", "build:test-report": "vue-cli-service build --mode build.test --report", "build:prod": "vue-cli-service build --mode build.prod && rm -fr ./dist/js/*.map", "build:perf-test": "bash scripts/build-performance-test.sh", "build:monitor": "node scripts/build-performance.js build", "build:compare": "node scripts/build-performance.js compare", "lint": "vue-cli-service --mode build.prod lint", "prepare": "husky install", "commit": "git-cz", "cache:clear": "node scripts/cache-manager.js", "cache:deep": "node scripts/cache-manager.js deep", "cache:reset": "node scripts/cache-manager.js full", "build:clean": "npm run cache:clear && npm run build:test", "dev:clean": "npm run cache:clear && npm run serve:test"}, "dependencies": {"@babel/runtime": "^7.28.2", "@microsoft/fetch-event-source": "^2.0.1", "@sentry/cli": "^2.50.2", "@sentry/vue": "^8.37.1", "@sentry/webpack-plugin": "^2.22.6", "@tinymce/tinymce-vue": "^3.2.6", "alife-logger": "^1.8.30", "axios": "^0.21.1", "crypto-js": "^4.1.1", "d3": "^7.3.0", "echarts": "^5.2.1", "element-ui": "^2.15.3", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "jszip": "^3.10.1", "langfuse": "^3.37.3", "langfuse-node": "^3.37.3", "lil-uri": "^0.2.2", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-link-attributes": "^4.0.1", "markdown-it-mathjax3": "^4.3.2", "mathjs": "^10.6.1", "moment": "^2.29.1", "number-precision": "^1.5.2", "qiniu-js": "^3.1.2", "qrcodejs2": "0.0.2", "resize-detector": "^0.3.0", "resize-observer-polyfill": "^1.5.1", "secure-ls": "^1.2.6", "socket.io-client": "^1.7.4", "solarlunar": "^2.0.7", "tinymce": "^6.2.0", "tippy.js": "^6.3.7", "ulid": "^2.3.0", "useless-files-webpack-plugin": "^1.0.1", "v-viewer": "^1.5.1", "vcolorpicker": "^1.1.0", "view-design": "^4.7.0", "vue": "^2.6.11", "vue-awesome-countdown": "^1.1.4", "vue-cropper": "^0.6.5", "vue-esign": "^1.1.4", "vue-ls": "^3.2.2", "vue-router": "^3.2.0", "vue-template-babel-compiler": "^2.0.0", "vue-ueditor-wrap": "^2.4.4", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0", "xlsx": "^0.17.3"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.27.2", "@babel/runtime-corejs3": "^7.28.2", "@commitlint/config-conventional": "^17.4.4", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.4.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.5", "babel-plugin-lodash": "^3.3.4", "chalk": "^4.1.2", "commitizen": "^4.3.0", "commitlint": "^17.5.1", "commitlint-config-cz": "^0.13.3", "compression-webpack-plugin": "^9.2.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^2.6.12", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "entities": "^2.2.0", "eslint": "^7.32.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "htmlparser2": "^6.1.0", "husky": "^8.0.3", "less": "^3.13.1", "less-loader": "^7.3.0", "lint-staged": "^13.2.0", "lodash-webpack-plugin": "^0.11.6", "mockjs": "^1.1.0", "node-object-hash": "^3.1.1", "ora": "^5.4.1", "prettier": "^2.2.1", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "^6.0.11", "terser-webpack-plugin": "^5.3.9", "thread-loader": "^3.0.4", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.9.0"}, "volta": {"node": "18.20.8", "npm": "10.8.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}